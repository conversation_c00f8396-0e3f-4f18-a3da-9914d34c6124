// internal/dao/mysql/migration.go
package mysql

import (
	"log"

	"file/internal/model"
)

// AutoMigrate 自动迁移数据库结构
func (m *MySQLManager) AutoMigrate() error {
	// 收集所有需要迁移的模型
	models := []interface{}{
		&model.User{},
		&model.File{},
		// 在这里添加其他模型
	}

	log.Println("开始自动迁移数据库...")

	// 执行自动迁移
	if err := m.master.AutoMigrate(models...); err != nil {
		return err
	}

	log.Println("数据库迁移完成")
	return nil
}
