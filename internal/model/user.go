// internal/model/user.go
package model

import (
	"gorm.io/gorm"
)

type User struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	CreatedAt gorm.DeletedAt `gorm:"index" json:"CreatedAt"`
	UpdatedAt gorm.DeletedAt `json:"UpdatedAt"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	Username string `gorm:"uniqueIndex;size:50;not null" json:"username"`
	Password string `gorm:"size:255;not null" json:"-"`
	Email    string `gorm:"size:100;not null" json:"email"`
}
