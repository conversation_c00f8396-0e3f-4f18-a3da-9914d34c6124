package model

import (
	"gorm.io/gorm"
	"time"
)

// File 文件模型
type File struct {
	ID        int64          `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	FileHash  string         `gorm:"column:file_shal;size:40;not null;uniqueIndex" json:"file_hash"`
	FileName  string         `gorm:"column:file_name;size:255;not null" json:"file_name"`
	FileSize  int64          `gorm:"column:file_size;default:0" json:"file_size"`
	FileAddr  string         `gorm:"column:file_addr;size:1024;not null" json:"file_addr"`
	CreatedAt time.Time      `gorm:"column:create_at;not null" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:update_at;not null" json:"updated_at"`
	Status    int            `gorm:"column:status;type:tinyint(1);default:1;index" json:"status"` // 1正常 0删除
	Ext1      string         `gorm:"column:ext_1;size:255" json:"ext_1"`
	Ext2      string         `gorm:"column:ext_2;size:255" json:"ext_2"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

const (
	FileStatusNormal  = 1 // 正常状态
	FileStatusDeleted = 0 // 删除状态
)
